// --- Canvas and Context ---
const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');
canvas.width = 800;
canvas.height = 600;

// --- Matter.js Aliases ---
const Engine = Matter.Engine;
const World = Matter.World;
const Bodies = Matter.Bodies;
const Body = Matter.Body;
const Events = Matter.Events;
const Composite = Matter.Composite;
const Query = Matter.Query;

// --- Game Engine Setup ---
let engine;
let world;

// --- Asset Management ---
const loadedImages = {};
let assetsToLoad = 0;
let assetsLoaded = 0;

// --- !!!! YOUR ANIMATION DEFINITIONS !!!! ---
const ANIMATION_DEFINITIONS = {
    // name: The key for this animation (e.g., 'idle', 'run')
    // fileNamePrefix: The part of the filename like 'fighter_Idle_' or 'fighter_run_'
    // startFrameNum: The number of the first frame in the sequence (from filename)
    // endFrameNum: The number of the last frame in the sequence
    // frameNumberDigits: How many digits for the frame number in filename (e.g., 4 for 0001)
    // frameWidth, frameHeight: Original dimensions of the PNG image (512x512 for you)
    // animationSpeed: Milliseconds per frame for this animation.
    // loop: true if the animation should loop.
    // physicsWidthFactor: Multiplier for SCALED frameWidth for physics body (e.g., 0.2 of scaled width)
    // physicsHeightFactor: Multiplier for SCALED frameHeight for physics body

    idle: { // Note: Your filename uses "Idle" (capital I)
        fileNamePrefix: 'fighter_Idle_',
        startFrameNum: 1, endFrameNum: 8, frameNumberDigits: 4,
        frameWidth: 512, frameHeight: 512, animationSpeed: 120, loop: true,
        physicsWidthFactor: 0.25, physicsHeightFactor: 0.45 // Scaled 512*PLAYER_SCALE * 0.25
    },
    walk: {
        fileNamePrefix: 'fighter_walk_',
        startFrameNum: 9, endFrameNum: 16, frameNumberDigits: 4,
        frameWidth: 512, frameHeight: 512, animationSpeed: 100, loop: true,
        physicsWidthFactor: 0.25, physicsHeightFactor: 0.45
    },
    run: {
        fileNamePrefix: 'fighter_run_',
        startFrameNum: 17, endFrameNum: 24, frameNumberDigits: 4,
        frameWidth: 512, frameHeight: 512, animationSpeed: 80, loop: true,
        physicsWidthFactor: 0.25, physicsHeightFactor: 0.45
    },
    jump: { // This will be the full jump sequence
        fileNamePrefix: 'fighter_jump_',
        startFrameNum: 43, endFrameNum: 47, frameNumberDigits: 4,
        frameWidth: 512, frameHeight: 512, animationSpeed: 80, loop: false,
        physicsWidthFactor: 0.25, physicsHeightFactor: 0.45
    },
    air_attack: {
        fileNamePrefix: 'fighter_air_attack_',
        startFrameNum: 62, endFrameNum: 63, frameNumberDigits: 4, // Very short, may need more or a hold
        frameWidth: 512, frameHeight: 512, animationSpeed: 100, loop: false,
        physicsWidthFactor: 0.3, physicsHeightFactor: 0.45 // Attacks might have slightly diff body
    },
    combo1: { // Assuming 'fighter_combo_xxxx.png' is one sequence
        fileNamePrefix: 'fighter_combo_',
        startFrameNum: 64, endFrameNum: 70, frameNumberDigits: 4, // Example: First part of combo
        frameWidth: 512, frameHeight: 512, animationSpeed: 70, loop: false,
        physicsWidthFactor: 0.3, physicsHeightFactor: 0.45
    },
    // You can define combo2, combo3 if 'fighter_combo_xxxx' is multiple attacks.
    // For now, combo1 is 64-70. Adjust as needed. Frame 71-82 would be more parts.
    dash: {
        fileNamePrefix: 'fighter_dash_',
        startFrameNum: 33, endFrameNum: 38, frameNumberDigits: 4,
        frameWidth: 512, frameHeight: 512, animationSpeed: 60, loop: false,
        physicsWidthFactor: 0.3, physicsHeightFactor: 0.40 // Dash might be lower profile
    },
    slide: {
        fileNamePrefix: 'fighter_slide_',
        startFrameNum: 25, endFrameNum: 32, frameNumberDigits: 4,
        frameWidth: 512, frameHeight: 512, animationSpeed: 70, loop: false,
        physicsWidthFactor: 0.3, physicsHeightFactor: 0.35 // Slide is low profile
    },
    hit: { // Player takes damage
        fileNamePrefix: 'fighter_hit_',
        startFrameNum: 48, endFrameNum: 51, frameNumberDigits: 4,
        frameWidth: 512, frameHeight: 512, animationSpeed: 90, loop: false,
        physicsWidthFactor: 0.25, physicsHeightFactor: 0.45
    },
    death: {
        fileNamePrefix: 'fighter_death_',
        startFrameNum: 52, endFrameNum: 61, frameNumberDigits: 4,
        frameWidth: 512, frameHeight: 512, animationSpeed: 100, loop: false,
        physicsWidthFactor: 0.25, physicsHeightFactor: 0.45 // Might change shape as they fall
    },
    climb: {
        fileNamePrefix: 'fighter_climb_',
        startFrameNum: 39, endFrameNum: 42, frameNumberDigits: 4,
        frameWidth: 512, frameHeight: 512, animationSpeed: 120, loop: true, // Climb usually loops
        physicsWidthFactor: 0.25, physicsHeightFactor: 0.45
    }
};
const PATH_PREFIX = 'Fighter sprites/'; // All your sprites are in this folder
const FILE_EXTENSION = '.png';

// --- Background Assets ---
const BACKGROUND_ASSETS = {
    // Only the specific assets requested
    background: ['background2.png'],
    trees: ['tree1.png', 'tree2.png'],
    ground: ['ground.png']
};

const backgroundImages = {};

function loadAssets(callback) {
    console.log("Starting asset loading...");

    // Load background images
    for (const category in BACKGROUND_ASSETS) {
        for (const filename of BACKGROUND_ASSETS[category]) {
            assetsToLoad++;
            const img = new Image();
            img.onload = () => {
                assetsLoaded++;
                if (assetsLoaded === assetsToLoad) {
                    console.log("All assets loaded successfully!");
                    callback();
                }
            };
            img.onerror = () => {
                console.error(`Failed to load background: extras/${filename}`);
                assetsLoaded++;
                if (assetsLoaded === assetsToLoad) callback();
            };
            img.src = `extras/${filename}`;
            backgroundImages[filename] = img;
        }
    }

    // Load animation sprites
    for (const animKey in ANIMATION_DEFINITIONS) {
        const anim = ANIMATION_DEFINITIONS[animKey];
        anim.imageObjects = [];
        anim.frameCount = anim.endFrameNum - anim.startFrameNum + 1; // Calculate actual frame count

        for (let i = 0; i < anim.frameCount; i++) {
            assetsToLoad++;
            const frameFileNumber = anim.startFrameNum + i;
            const frameNumberPadded = frameFileNumber.toString().padStart(anim.frameNumberDigits, '0');
            const path = `${PATH_PREFIX}${anim.fileNamePrefix}${frameNumberPadded}${FILE_EXTENSION}`;

            const img = new Image();
            img.onload = () => {
                assetsLoaded++;
                if (assetsLoaded === assetsToLoad) {
                    console.log("All assets loaded successfully!");
                    callback();
                }
            };
            img.onerror = () => {
                console.error(`Failed to load: ${path}`);
                assetsLoaded++; // Still count it
                if (assetsLoaded === assetsToLoad) callback();
            };
            img.src = path;
            loadedImages[path] = img;
            anim.imageObjects.push(img); // Images are pushed in order of the animation sequence
        }
    }
    if (assetsToLoad === 0) {
        console.warn("No assets defined or to load.");
        callback();
    }
}

// --- Game Objects ---
let player;
let ground; // Will add more platforms later
let backgroundRenderer;

// --- Constants ---
const PLAYER_SCALE = 0.5; // !!! CRITICAL: Adjust this to make your 512x512 sprites fit nicely !!!
                           // Try values like 0.15, 0.2, 0.25, 0.3, 0.35
const PLAYER_DENSITY = 0.0005;
const PLAYER_FRICTION = 0.1;
const PLAYER_RESTITUTION = 0.0;
const PLAYER_WALK_FORCE = 0.0025; // Walking speed
const PLAYER_RUN_FORCE = 0.0105;  // Running speed (80% faster)
const PLAYER_JUMP_FORCE = 0.1;   // Adjusted

class Stickman {
    constructor(x, y, initialAnimName = 'idle') {
        this.currentAnimationName = initialAnimName;
        this.currentAnimation = ANIMATION_DEFINITIONS[this.currentAnimationName];
        if (!this.currentAnimation) {
            console.error(`Initial animation "${initialAnimName}" not defined! Defaulting...`);
            this.currentAnimationName = Object.keys(ANIMATION_DEFINITIONS)[0];
            this.currentAnimation = ANIMATION_DEFINITIONS[this.currentAnimationName];
        }

        this.scaledFrameWidth = this.currentAnimation.frameWidth * PLAYER_SCALE;
        this.scaledFrameHeight = this.currentAnimation.frameHeight * PLAYER_SCALE;

        this.currentFrameIndex = 0;
        this.animationTimer = 0;
        this.facingDirection = 1;
        this.isGrounded = false;
        this.isAttacking = false;
        this.isTakingDamage = false;
        this.isDead = false;
        this.isRunning = false; // Toggle state for run/walk
        // ... more states: isDashing, isSliding

        const bodyWidth = this.scaledFrameWidth * this.currentAnimation.physicsWidthFactor;
        const bodyHeight = this.scaledFrameHeight * this.currentAnimation.physicsHeightFactor;

        this.body = Bodies.rectangle(x, y, bodyWidth, bodyHeight, {
            density: PLAYER_DENSITY,
            friction: PLAYER_FRICTION,
            restitution: PLAYER_RESTITUTION,
            label: 'player'
        });
        Body.setInertia(this.body, Infinity);
        console.log(`Player body created. Size: ${bodyWidth.toFixed(2)}x${bodyHeight.toFixed(2)} at ${x},${y}`);
    }

    setAnimation(animName, forceInterrupt = false) {
        if (!ANIMATION_DEFINITIONS[animName]) {
            console.warn(`Animation "${animName}" not found.`);
            return;
        }
        if (this.currentAnimationName === animName && !forceInterrupt) return;

        // Prevent interrupting certain animations unless forced
        if (!forceInterrupt) {
            if (this.isAttacking && !animName.includes('combo') && animName !=='hit' && animName !=='death') return; // Don't interrupt attack unless it's another attack, hit or death
            if (this.isTakingDamage && animName !== 'death') return; // Don't interrupt hit unless death
            if (this.isDead) return; // Dead is dead
        }
        if (this.isDead && animName !== 'death') return; // Stay dead


        this.currentAnimationName = animName;
        this.currentAnimation = ANIMATION_DEFINITIONS[this.currentAnimationName];
        this.currentFrameIndex = 0;
        this.animationTimer = 0;

        // Update state flags based on new animation
        this.isAttacking = this.currentAnimationName.includes('combo') || this.currentAnimationName.includes('air_attack');
        this.isTakingDamage = this.currentAnimationName === 'hit';

        // Adjust rendered size based on the new animation's base frame size (if they vary significantly)
        this.scaledFrameWidth = this.currentAnimation.frameWidth * PLAYER_SCALE;
        this.scaledFrameHeight = this.currentAnimation.frameHeight * PLAYER_SCALE;

        // IMPORTANT: If animations have very different character postures,
        // you might need to adjust the physics body size or offset here.
        // For simplicity now, physics body size is fixed.
    }

    update(deltaTime) {
        if (this.isDead) {
             if(this.currentAnimationName !== 'death') this.setAnimation('death', true);
             // Keep advancing death animation if it has frames and isn't looping
             if (!this.currentAnimation.loop && this.currentFrameIndex < this.currentAnimation.frameCount -1){
                this.animationTimer += deltaTime;
                if (this.animationTimer >= this.currentAnimation.animationSpeed) {
                    this.animationTimer = 0;
                    this.currentFrameIndex++;
                }
             }
            return; // No further updates if dead
        }


        // Animation frame progression
        this.animationTimer += deltaTime;
        if (this.animationTimer >= this.currentAnimation.animationSpeed) {
            this.animationTimer = 0;
            if (this.currentFrameIndex < this.currentAnimation.frameCount - 1) {
                this.currentFrameIndex++;
            } else if (this.currentAnimation.loop) {
                this.currentFrameIndex = 0;
            } else { // Non-looping animation finished
                if (this.isAttacking) this.isAttacking = false;
                if (this.isTakingDamage) this.isTakingDamage = false;
                // Default to idle/in-air state after non-looping anim finishes
                this.setAnimation(this.isGrounded ? 'idle' : 'jump', true);
            }
        }

        this.isGrounded = this.checkGrounded();

        // State logic (don't override if attacking, taking damage, etc.)
        if (!this.isAttacking && !this.isTakingDamage) {
            if (this.isGrounded) {
                if (Math.abs(this.body.velocity.x) > 0.1) { // Character is moving
                    if (this.isRunning) {
                        this.setAnimation('run');
                    } else {
                        this.setAnimation('walk');
                    }
                } else {
                    this.setAnimation('idle');
                }
            } else { // In Air
                // Simplistic jump/fall for now - just one "jump" animation.
                // Could differentiate between upward and downward velocity for jump/fall animations.
                if (this.currentAnimationName !== 'jump' && this.currentAnimationName !== 'air_attack') {
                     this.setAnimation('jump');
                }
            }
        }

        if (this.body.velocity.x > 0.1 && !this.isAttacking) this.facingDirection = 1;
        else if (this.body.velocity.x < -0.1 && !this.isAttacking) this.facingDirection = -1;

        // Simulate health and death for testing 'hit' and 'death' animations
        // if (keys['h'] && !this.isTakingDamage && !this.isDead) { // Test hit key
        //     this.takeDamage();
        //     keys['h'] = false;
        // }
        // if (keys['k'] && !this.isDead) { // Test kill key
        //     this.die();
        //     keys['k'] = false;
        // }
    }

    draw() {
        const currentFrameImage = this.currentAnimation.imageObjects[this.currentFrameIndex];
        if (!currentFrameImage || !currentFrameImage.complete || currentFrameImage.naturalWidth === 0) {
            // Draw placeholder...
            ctx.fillStyle = 'rgba(100,100,100,0.5)';
            const phX = this.body.position.x - this.scaledFrameWidth / 2;
            const phY = this.body.position.y - this.scaledFrameHeight / 2;
            ctx.fillRect(phX, phY, this.scaledFrameWidth, this.scaledFrameHeight);
            return;
        }

        const sourceWidth = currentFrameImage.naturalWidth; // Should be 512
        const sourceHeight = currentFrameImage.naturalHeight; // Should be 512

        const pos = this.body.position;
        ctx.save();
        ctx.translate(pos.x, pos.y);
        if (this.facingDirection === -1) ctx.scale(-1, 1);

        // We draw the full 512x512 frame, but scaled down.
        // The character art should be centered within this frame by the artist.
        // The physics body is smaller than this scaled visual.
        ctx.drawImage(
            currentFrameImage,
            0, 0, sourceWidth, sourceHeight,
            -this.scaledFrameWidth / 2, -this.scaledFrameHeight / 2,
            this.scaledFrameWidth, this.scaledFrameHeight
        );
        ctx.restore();

        // Optional: Draw physics body outline for debugging
        ctx.beginPath();
        const vertices = this.body.vertices;
        ctx.moveTo(vertices[0].x, vertices[0].y);
        for (let i = 1; i < vertices.length; i++) ctx.lineTo(vertices[i].x, vertices[i].y);
        ctx.lineTo(vertices[0].x, vertices[0].y);
        ctx.lineWidth = 2; ctx.strokeStyle = '#00FF00'; ctx.stroke();
    }

    move(direction) {
        if (this.isAttacking || this.isDead || this.isTakingDamage) return;
        const moveForce = this.isRunning ? PLAYER_RUN_FORCE : PLAYER_WALK_FORCE;
        Body.applyForce(this.body, this.body.position, { x: moveForce * direction, y: 0 });
    }

    toggleRunning() {
        this.isRunning = !this.isRunning;
        console.log(`Running mode: ${this.isRunning ? 'ON' : 'OFF'}`);
    }

    jump() {
        if (this.isGrounded && !this.isAttacking && !this.isDead && !this.isTakingDamage) {
            Body.applyForce(this.body, this.body.position, { x: 0, y: -PLAYER_JUMP_FORCE });
            this.setAnimation('jump', true); // Force interrupt for jump
            this.isGrounded = false; // Debounce
        }
    }

    attack(comboPart) { // e.g., 'combo1', 'air_attack'
        if (this.isDead || this.isTakingDamage) return;

        if (comboPart === 'air_attack' && !this.isGrounded && !this.isAttacking) {
             this.setAnimation('air_attack', true);
             // Optional: Modify velocity slightly for air attack feel
        } else if (this.isGrounded && !this.isAttacking) {
            if (ANIMATION_DEFINITIONS[comboPart]) {
                this.setAnimation(comboPart, true);
                Body.setVelocity(this.body, { x: this.body.velocity.x * 0.1, y: this.body.velocity.y }); // Slight slow down
            }
        }
    }

    takeDamage() {
        if (this.isDead) return;
        console.log("Player took damage!");
        this.setAnimation('hit', true); // force interrupt
        // Add health logic here
        // if health <= 0, call this.die()
    }
    die(){
        if(this.isDead) return;
        console.log("Player died!");
        this.isDead = true;
        this.setAnimation('death', true);
        Body.setStatic(this.body, true); // Make body static on death or apply different physics
    }


    checkGrounded() {
        const bodyHalfHeight = (this.scaledFrameHeight * this.currentAnimation.physicsHeightFactor) / 2;
        const rayStart = { x: this.body.position.x, y: this.body.position.y + bodyHalfHeight -1 }; // Start just inside bottom
        const rayEnd = { x: this.body.position.x, y: this.body.position.y + bodyHalfHeight + 5 }; // Check 5 pixels down

        const allBodiesInWorld = Composite.allBodies(world); // Get all bodies from the world composite
        const collisions = Query.ray(allBodiesInWorld, rayStart, rayEnd);
        // Debug Draw Ray
        // ctx.beginPath(); ctx.moveTo(rayStart.x, rayStart.y); ctx.lineTo(rayEnd.x, rayEnd.y);
        // ctx.strokeStyle = 'rgba(255,0,0,0.5)'; ctx.lineWidth=1; ctx.stroke();

        for (let i = 0; i < collisions.length; i++) {
            if (collisions[i].bodyA !== this.body && collisions[i].bodyB !== this.body) {
                 if (collisions[i].bodyA.label === 'ground' || collisions[i].bodyB.label === 'ground') {
                    return true;
                }
            }
        }
        return false;
    }
}

// --- Background Rendering System ---
class BackgroundRenderer {
    constructor() {
        // Simple setup - no complex environment elements
    }

    drawBackground() {
        // Draw background2.png as the main background
        const bgImage = backgroundImages['background2.png'];

        if (bgImage && bgImage.complete) {
            // Scale background to fit canvas
            ctx.drawImage(bgImage, 0, 0, canvas.width, canvas.height);
        } else {
            // Fallback to colored background
            ctx.fillStyle = '#87CEEB'; // Sky blue
            ctx.fillRect(0, 0, canvas.width, canvas.height);
        }
    }

    drawGround() {
        // Draw ground.png as the ground surface
        const groundImg = backgroundImages['ground.png'];
        if (groundImg && groundImg.complete) {
            // Position ground at the bottom, scale to fit width
            const groundHeight = 70; // Adjust as needed
            const groundWidth = canvas.width;
            ctx.drawImage(groundImg, 0, canvas.height - groundHeight + 10, groundWidth, groundHeight);
        }
    }

    drawBuildings() {
        // Draw ground.png as the ground surface
        const groundImg = backgroundImages['building1.png'];
        if (groundImg && groundImg.complete) {
            // Position ground at the bottom, scale to fit width
            const groundHeight = 70; // Adjust as needed
            const groundWidth = canvas.width;
            ctx.drawImage(groundImg, 60, canvas.height - groundHeight + 10, groundWidth, groundHeight);
        }
    }

    drawTrees() {
        // Tree2 on the very left
        const tree2Img = backgroundImages['tree2.png'];
        if (tree2Img && tree2Img.complete) {
            const tree2Scale = 0.5;
            const tree2Width = tree2Img.naturalWidth * tree2Scale;
            const tree2Height = tree2Img.naturalHeight * tree2Scale;
            ctx.drawImage(tree2Img, -90, canvas.height - tree2Height - 30, tree2Width, tree2Height);
        }

        // Tree1 on the very right
        const tree1Img = backgroundImages['tree1.png'];
        if (tree1Img && tree1Img.complete) {
            const tree1Scale = 0.75;
            const tree1Width = tree1Img.naturalWidth * tree1Scale;
            const tree1Height = tree1Img.naturalHeight * tree1Scale;
            ctx.drawImage(tree1Img, canvas.width - tree1Width + 60, canvas.height - tree1Height - 50, tree1Width, tree1Height);
        }
    }

    render() {
        // Draw background
        this.drawBackground();

        

        // Draw trees behind the player
        this.drawTrees();
        this.drawBuildings()

        // Draw ground surface
        this.drawGround();
    }

    renderForeground() {
        // No foreground elements needed
    }
}

// --- Input Handling ---
const keys = {};
document.addEventListener('keydown', (e) => {
    const key = e.key.toLowerCase();
    keys[key] = true;

    if (key === 'j') {
        if(player && !player.isGrounded) player.attack('air_attack');
        else if (player) player.attack('combo1'); // Ground attack
        // We don't set keys['j'] = false here, so holding might lead to re-triggering if conditions allow
    }
    if (key === 'w' || key === 'arrowup' || key === ' ') {
        if(player) player.jump();
        // Prevent continuous jump by handling action on keydown and not relying on continuous check
    }
    if (key === 'h' && player) player.takeDamage(); // Test damage
    if (key === 'k' && player) player.die();      // Test death
    if (key === 'shift' && player) {
        player.toggleRunning(); // Toggle between run and walk
        keys['shift'] = false; // Prevent continuous toggling
    }
});
document.addEventListener('keyup', (e) => keys[e.key.toLowerCase()] = false);

function handleContinuousInput() {
    if (!player || player.isDead) return;

    if (keys['a'] || keys['arrowleft']) {
        player.move(-1);
    } else if (keys['d'] || keys['arrowright']) {
        player.move(1);
    }
}

// --- Game Initialization ---
function initGame() {
    engine = Engine.create();
    world = engine.world;
    engine.gravity.y = 1;

    // Initialize background renderer
    backgroundRenderer = new BackgroundRenderer();

    const initialAnim = ANIMATION_DEFINITIONS.idle; // Or whichever is your defined starting anim
    const pScaledHeight = initialAnim.frameHeight * PLAYER_SCALE;
    const pBodyHeight = pScaledHeight * initialAnim.physicsHeightFactor;

    player = new Stickman(
        canvas.width / 4,
        canvas.height - 50 - (pBodyHeight / 2) - 5, // Y adjusted for physics body center on ground
        'idle'
    );
    ground = Bodies.rectangle(canvas.width / 2, canvas.height - 25, canvas.width + 100, 50, { isStatic: true, label: 'ground', friction: 0.5 });
    World.add(world, [player.body, ground]);

    console.log("Game initialized. Player created.");
    lastTime = performance.now();
    requestAnimationFrame(gameLoop); // Start the loop after init
}

// --- Game Loop ---
let lastTime = 0;
function gameLoop(currentTime) {
    const deltaTime = (currentTime - lastTime) || (1000/60); // Handle potential 0 delta on first frame
    lastTime = currentTime;

    handleContinuousInput();

    if (player) player.update(deltaTime);
    Engine.update(engine, deltaTime);

    // Render background layers
    if (backgroundRenderer) {
        backgroundRenderer.render();
    } else {
        // Fallback background
        ctx.fillStyle = '#3498db';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
    }

    // Physics ground is now invisible - visual ground is handled by background renderer

    // Draw player
    if (player) player.draw();

    // Render foreground elements (grass, rocks, etc.)
    if (backgroundRenderer) {
        backgroundRenderer.renderForeground();
    }

    // Draw UI indicators
    if (player) {
        // Running mode indicator
        ctx.fillStyle = player.isRunning ? '#FF6B6B' : '#4ECDC4';
        ctx.font = '16px Arial';
        ctx.fillText(`Mode: ${player.isRunning ? 'RUN' : 'WALK'} (Shift to toggle)`, 10, 30);

        // Controls
        ctx.fillStyle = '#FFFFFF';
        ctx.font = '12px Arial';
        ctx.fillText('Controls: A/D-Move, Space-Jump, J-Attack, Shift-Run/Walk', 10, canvas.height - 10);
    }

    requestAnimationFrame(gameLoop);
}

// Start asset loading, then initialize the game
console.log("Requesting asset load...");
loadAssets(initGame);